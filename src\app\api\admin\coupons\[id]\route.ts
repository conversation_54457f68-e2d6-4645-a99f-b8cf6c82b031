import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

// GET - Ottieni dettagli coupon
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const supabase = await createClient()

    // Verifica autenticazione admin
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Non autorizzato' }, { status: 401 })
    }

    // Verifica se l'utente è admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json({ error: 'Accesso negato' }, { status: 403 })
    }

    // Ottieni il coupon
    const { data: coupon, error } = await supabase
      .from('coupons')
      .select('*')
      .eq('id', resolvedParams.id)
      .single()

    if (error || !coupon) {
      return NextResponse.json({ error: 'Coupon non trovato' }, { status: 404 })
    }

    return NextResponse.json({ success: true, coupon })
  } catch (error) {
    console.error('Errore nel recupero coupon:', error)
    return NextResponse.json({ error: 'Errore interno del server' }, { status: 500 })
  }
}

// PUT - Aggiorna coupon
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params
    const supabase = await createClient()

    // Verifica autenticazione admin
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Non autorizzato' }, { status: 401 })
    }

    // Verifica se l'utente è admin
    const { data: profile } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json({ error: 'Accesso negato' }, { status: 403 })
    }

    const body = await request.json()
    const { code, type, value, minimumOrderAmount, usageLimit, validUntil, isActive } = body

    // Validazione
    if (!code || !type || value === undefined || !validUntil) {
      return NextResponse.json({ error: 'Campi obbligatori mancanti' }, { status: 400 })
    }

    if (!['percentage', 'fixed_amount'].includes(type)) {
      return NextResponse.json({ error: 'Tipo coupon non valido' }, { status: 400 })
    }

    if (type === 'percentage' && (value < 0 || value > 100)) {
      return NextResponse.json({ error: 'La percentuale deve essere tra 0 e 100' }, { status: 400 })
    }

    if (type === 'fixed_amount' && value < 0) {
      return NextResponse.json({ error: 'Importo fisso deve essere positivo' }, { status: 400 })
    }

    // Verifica se esiste già un coupon con lo stesso codice (escludendo quello corrente)
    const { data: existingCoupon } = await supabase
      .from('coupons')
      .select('id')
      .eq('code', code.toUpperCase())
      .neq('id', resolvedParams.id)
      .single()

    if (existingCoupon) {
      return NextResponse.json({ error: 'Ein Gutschein mit diesem Code existiert bereits' }, { status: 400 })
    }

    // Aggiorna il coupon
    const { data: coupon, error } = await supabase
      .from('coupons')
      .update({
        code: code.toUpperCase(),
        type,
        value: parseFloat(value.toString()),
        minimum_order_amount: minimumOrderAmount ? parseFloat(minimumOrderAmount.toString()) : null,
        usage_limit: usageLimit ? parseInt(usageLimit.toString()) : null,
        valid_until: validUntil,
        is_active: isActive
      })
      .eq('id', resolvedParams.id)
      .select()
      .single()

    if (error) {
      console.error('Errore nell\'aggiornamento coupon:', error)
      return NextResponse.json({ error: 'Errore nell\'aggiornamento del coupon' }, { status: 500 })
    }

    return NextResponse.json({ success: true, coupon })
  } catch (error) {
    console.error('Errore nell\'aggiornamento coupon:', error)
    return NextResponse.json({ error: 'Errore interno del server' }, { status: 500 })
  }
}
