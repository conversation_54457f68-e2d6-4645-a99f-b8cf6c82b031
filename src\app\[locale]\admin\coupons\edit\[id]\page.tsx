'use client'

import { useState, useEffect } from 'react'
import { useTranslations, useLocale } from 'next-intl'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { useToast } from '@/hooks/use-toast'
import { AdminBackButton } from '@/components/admin/admin-back-button'
import { ArrowLeft, Percent, DollarSign, Loader2 } from 'lucide-react'
import Link from 'next/link'

interface CouponData {
  id: string
  code: string
  type: 'percentage' | 'fixed_amount'
  value: number
  minimum_order_amount?: number
  usage_limit?: number
  valid_until: string
  is_active: boolean
}

export default function EditCouponPage({ params }: { params: Promise<{ id: string }> }) {
  const t = useTranslations('admin')
  const locale = useLocale()
  const router = useRouter()
  const { toast } = useToast()

  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [coupon, setCoupon] = useState<CouponData | null>(null)
  const [formData, setFormData] = useState({
    code: '',
    type: 'percentage' as 'percentage' | 'fixed_amount',
    value: 0,
    minimumOrderAmount: '',
    usageLimit: '',
    validUntil: '',
    isActive: true
  })

  // Carica i dati del coupon
  useEffect(() => {
    const fetchCoupon = async () => {
      try {
        const resolvedParams = await params
        const response = await fetch(`/api/admin/coupons/${resolvedParams.id}`)
        const data = await response.json()

        if (response.ok && data.coupon) {
          const couponData = data.coupon
          setCoupon(couponData)

          // Formatta la data per l'input datetime-local
          const validUntilDate = new Date(couponData.valid_until)
          const formattedDate = validUntilDate.toISOString().slice(0, 16)

          setFormData({
            code: couponData.code,
            type: couponData.type,
            value: couponData.value,
            minimumOrderAmount: couponData.minimum_order_amount?.toString() || '',
            usageLimit: couponData.usage_limit?.toString() || '',
            validUntil: formattedDate,
            isActive: couponData.is_active
          })
        } else {
          toast({
            title: t('common.error'),
            description: 'Coupon non trovato',
            variant: 'destructive',
          })
          router.push(`/${locale}/admin/coupons`)
        }
      } catch (error) {
        console.error('Errore nel caricamento coupon:', error)
        toast({
          title: t('common.error'),
          description: 'Errore nel caricamento del coupon',
          variant: 'destructive',
        })
        router.push(`/${locale}/admin/coupons`)
      } finally {
        setLoading(false)
      }
    }

    fetchCoupon()
  }, [params, router, locale, toast, t])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)

    try {
      const resolvedParams = await params
      const response = await fetch(`/api/admin/coupons/${resolvedParams.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: formData.code,
          type: formData.type,
          value: formData.value,
          minimumOrderAmount: formData.minimumOrderAmount || null,
          usageLimit: formData.usageLimit || null,
          validUntil: formData.validUntil,
          isActive: formData.isActive,
        }),
      })

      const data = await response.json()

      if (response.ok && data.success) {
        toast({
          title: t('couponsPage.couponUpdated'),
          description: t('couponsPage.couponUpdatedDesc'),
          variant: 'default',
        })
        router.push(`/${locale}/admin/coupons`)
      } else {
        toast({
          title: t('common.error'),
          description: data.error || t('couponsPage.updateError'),
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('Errore nell\'aggiornamento coupon:', error)
      toast({
        title: t('common.error'),
        description: t('couponsPage.updateError'),
        variant: 'destructive',
      })
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    )
  }

  if (!coupon) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <p className="text-muted-foreground">Coupon non trovato</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      {/* Back to Dashboard Button */}
      <div className="mb-6">
        <AdminBackButton />
      </div>

      <div className="flex items-center gap-4 mb-8">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/${locale}/admin/coupons`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('common.back')}
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold">{t('couponsPage.editCoupon')}</h1>
        </div>
      </div>

      <Card className="max-w-2xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Percent className="h-5 w-5" />
            {t('couponsPage.editCoupon')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Codice Coupon */}
            <div className="space-y-2">
              <Label htmlFor="code">{t('couponsPage.code')} *</Label>
              <Input
                id="code"
                value={formData.code}
                onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                placeholder="es. SAVE10"
                required
                className="font-mono"
              />
            </div>

            {/* Tipo Coupon */}
            <div className="space-y-2">
              <Label>{t('couponsPage.type')} *</Label>
              <Select
                value={formData.type}
                onValueChange={(value: 'percentage' | 'fixed_amount') => 
                  setFormData({ ...formData, type: value, value: 0 })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="percentage">
                    <div className="flex items-center gap-2">
                      <Percent className="h-4 w-4" />
                      {t('couponsPage.percentage')}
                    </div>
                  </SelectItem>
                  <SelectItem value="fixed_amount">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      {t('couponsPage.fixedAmount')}
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Valore */}
            <div className="space-y-2">
              <Label htmlFor="value">
                {formData.type === 'percentage' 
                  ? `${t('couponsPage.percentage')} (%) *`
                  : `${t('couponsPage.amount')} (CHF) *`
                }
              </Label>
              <Input
                id="value"
                type="number"
                step={formData.type === 'percentage' ? '1' : '0.01'}
                min="0"
                max={formData.type === 'percentage' ? '100' : undefined}
                value={formData.value}
                onChange={(e) => setFormData({ ...formData, value: parseFloat(e.target.value) || 0 })}
                required
              />
            </div>

            {/* Ordine Minimo */}
            <div className="space-y-2">
              <Label htmlFor="minimumOrder">{t('couponsPage.minimumOrder')} (CHF)</Label>
              <Input
                id="minimumOrder"
                type="number"
                step="0.01"
                min="0"
                value={formData.minimumOrderAmount}
                onChange={(e) => setFormData({ ...formData, minimumOrderAmount: e.target.value })}
                placeholder="0.00"
              />
            </div>

            {/* Limite Utilizzi */}
            <div className="space-y-2">
              <Label htmlFor="usageLimit">{t('couponsPage.usageLimit')}</Label>
              <Input
                id="usageLimit"
                type="number"
                min="1"
                value={formData.usageLimit}
                onChange={(e) => setFormData({ ...formData, usageLimit: e.target.value })}
                placeholder={t('couponsPage.unlimited')}
              />
            </div>

            {/* Data Scadenza */}
            <div className="space-y-2">
              <Label htmlFor="validUntil">{t('couponsPage.validUntil')} *</Label>
              <Input
                id="validUntil"
                type="datetime-local"
                value={formData.validUntil}
                onChange={(e) => setFormData({ ...formData, validUntil: e.target.value })}
                required
              />
            </div>

            {/* Attivo */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => setFormData({ ...formData, isActive: !!checked })}
              />
              <Label htmlFor="isActive">{t('couponsPage.isActive')}</Label>
            </div>

            {/* Pulsanti */}
            <div className="flex gap-4 pt-4">
              <Button type="submit" disabled={saving}>
                {saving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t('common.updating')}
                  </>
                ) : (
                  t('couponsPage.updateCoupon')
                )}
              </Button>
              <Button type="button" variant="outline" asChild>
                <Link href={`/${locale}/admin/coupons`}>
                  {t('common.cancel')}
                </Link>
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
